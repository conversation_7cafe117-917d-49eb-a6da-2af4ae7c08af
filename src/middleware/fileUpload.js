import multer from 'multer';
import { ResponseUtil  } from '../utils/response.js';
import logger from '../config/logger.js';

// Configure multer for memory storage
const storage = multer.memoryStorage();

/**
 * Check if file is a code file based on extension
 * @param {string} filename - Original filename
 * @returns {boolean} Whether file is a code file
 */
const isCodeFile = (filename) => {
  const codeExtensions = [
    '.js', '.jsx', '.ts', '.tsx', '.mjs', '.cjs',
    '.py', '.pyw', '.pyc', '.pyo', '.pyd',
    '.java', '.class', '.jar',
    '.c', '.cpp', '.cxx', '.cc', '.h', '.hpp', '.hxx',
    '.cs', '.vb', '.fs', '.fsx',
    '.php', '.php3', '.php4', '.php5', '.phtml',
    '.rb', '.rbw', '.gem',
    '.go', '.mod', '.sum',
    '.rs', '.rlib',
    '.swift',
    '.kt', '.kts',
    '.scala', '.sc',
    '.r', '.R', '.rmd',
    '.m', '.mm',
    '.pl', '.pm', '.t', '.pod',
    '.sh', '.bash', '.zsh', '.fish', '.csh', '.tcsh',
    '.ps1', '.psm1', '.psd1',
    '.bat', '.cmd',
    '.sql', '.mysql', '.pgsql', '.sqlite',
    '.html', '.htm', '.xhtml',
    '.css', '.scss', '.sass', '.less', '.styl',
    '.xml', '.xsl', '.xslt', '.dtd', '.xsd',
    '.json', '.jsonl', '.json5',
    '.yaml', '.yml',
    '.toml', '.ini', '.cfg', '.conf',
    '.md', '.markdown', '.mdown', '.mkd',
    '.txt', '.text', '.log',
    '.dockerfile', '.dockerignore',
    '.gitignore', '.gitattributes',
    '.env', '.env.local', '.env.development', '.env.production',
    '.makefile', '.cmake', '.gradle',
    '.vue', '.svelte',
    '.dart', '.lua', '.nim', '.zig', '.v'
  ];

  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
  return codeExtensions.includes(extension) || filename.toLowerCase().includes('makefile');
};

// File filter to validate file types
const fileFilter = (req, file, cb) => {
  const allowedMimeTypes = [
    // Images
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    // Documents
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    // Text files
    'text/plain',
    'text/markdown',
    'application/json'
  ];

  // Reject video files explicitly
  if (file.mimetype.startsWith('video/')) {
    logger.warn(`Rejected video file upload: ${file.originalname} (${file.mimetype})`);
    return cb(new Error('Video files are not supported'));
  }

  // Check if it's a code file - if so, allow it regardless of MIME type
  if (isCodeFile(file.originalname)) {
    logger.info(`Accepted code file upload: ${file.originalname} (${file.mimetype}) - will be processed as plain text`);
    cb(null, true);
    return;
  }

  // Check against allowed MIME types for non-code files
  if (allowedMimeTypes.includes(file.mimetype)) {
    logger.info(`Accepted file upload: ${file.originalname} (${file.mimetype})`);
    cb(null, true);
  } else {
    logger.warn(`Rejected file upload: ${file.originalname} (${file.mimetype})`);
    cb(new Error(`Unsupported file type: ${file.mimetype}. Supported types: images, PDFs, Word documents, Excel files, text files, and code files.`));
  }
};

// Configure multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1 // Only allow 1 file per request
  }
});

/**
 * Middleware for handling single file upload
 */
const uploadSingleFile = upload.single('attachment');

/**
 * Error handling middleware for file upload errors
 * @param {Object} error - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const handleFileUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    logger.error('Multer error:', error);
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        ResponseUtil.validationError(res, 'File size exceeds the maximum limit of 5MB');
        return;
      case 'LIMIT_FILE_COUNT':
        ResponseUtil.validationError(res, 'Only one file can be uploaded at a time');
        return;
      case 'LIMIT_UNEXPECTED_FILE':
        ResponseUtil.validationError(res, 'Unexpected file field. Use "attachment" field name');
        return;
      default:
        ResponseUtil.validationError(res, `File upload error: ${error.message}`);
        return;
    }
  }

  if (error.message) {
    logger.error('File upload error:', error.message);
    ResponseUtil.validationError(res, error.message);
    return;
  }

  next(error);
};

/**
 * Middleware to check if file was uploaded
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requireFile = (req, res, next) => {
  if (!req.file) {
    ResponseUtil.validationError(res, 'No file uploaded');
    return;
  }
  next();
};

/**
 * Middleware to make file upload optional
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const optionalFile = (req, res, next) => {
  // File is optional, continue regardless
  next();
};

export { uploadSingleFile,
  handleFileUploadError,
  requireFile,
  optionalFile
 };
