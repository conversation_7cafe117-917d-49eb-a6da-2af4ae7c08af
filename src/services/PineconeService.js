import { Pinecone } from '@pinecone-database/pinecone';
import { OpenAI } from 'openai';
import logger from '../config/logger.js';

export class PineconeService {
  static client = null;
  static index = null;
  static openai = null;

  /**
   * Initialize Pinecone client
   * @returns {Promise<void>}
   */
  static async initialize() {
    try {
      if (!process.env.PINECONE_API_KEY) {
        logger.warn('Pinecone API key not found. Pinecone service will be disabled.');
        return;
      }

      this.client = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY,
      });

      const indexName = process.env.PINECONE_INDEX_NAME || 'theinfini-ai-chat';
      this.index = this.client.index(indexName);

      // Initialize OpenAI client for embeddings
      if (process.env.OPENAI_API_KEY) {
        this.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });
        logger.info('OpenAI client initialized for embeddings');
      } else {
        logger.warn('OpenAI API key not found. Will use fallback embedding method.');
      }

      logger.info('Pinecone service initialized successfully');
    } catch (error) {
      logger.error('Error initializing Pinecone service:', error);
      throw error;
    }
  }

  /**
   * Check if Pinecone is available
   * @returns {boolean} Whether Pinecone is available
   */
  static isAvailable() {
    return !!this.client && !!this.index;
  }

  /**
   * Generate embedding for text using OpenAI
   * @param {string} text - Text to generate embedding for
   * @returns {Promise<Array<number>>} Embedding vector
   */
  static async generateEmbedding(text) {
    try {
      if (this.openai) {
        // Use OpenAI's embedding API with dimension matching Pinecone index
        const response = await this.openai.embeddings.create({
          model: 'text-embedding-3-small',
          input: text,
          dimensions: 1536, // Match Pinecone index dimension
        });

        return response.data[0].embedding;
      } else {
        // Fallback to hash-based embedding
        const { createHash } = await import('crypto');
        const hash = createHash('sha256').update(text).digest('hex');

        // Convert hash to a 1536-dimensional vector (OpenAI embedding size)
        const vector = [];
        for (let i = 0; i < 1536; i++) {
          const charCode = hash.charCodeAt(i % hash.length);
          vector.push((charCode / 255) * 2 - 1); // Normalize to [-1, 1]
        }

        return vector;
      }
    } catch (error) {
      logger.error('Error generating embedding:', error);
      throw error;
    }
  }

  /**
   * Store chat message in Pinecone
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} messageId - Message ID
   * @param {string} message - User message
   * @param {string} response - Assistant response
   * @param {Object} [metadata] - Additional metadata
   * @returns {Promise<void>}
   */
  static async storeMessage(
    projectId,
    threadId,
    messageId,
    message,
    response,
    metadata = {}
  ) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping message storage');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      const combinedText = `User: ${message}\nAssistant: ${response}`;
      const embedding = await this.generateEmbedding(combinedText);

      await this.index.namespace(namespace).upsert([
        {
          id: messageId,
          values: embedding,
          metadata: {
            projectId,
            threadId,
            message,
            response,
            timestamp: new Date().toISOString(),
            ...metadata,
          },
        },
      ]);

      logger.debug(`Stored message in Pinecone: ${messageId}`);
    } catch (error) {
      logger.error('Error storing message in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Search for similar messages in project context
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} query - Search query
   * @param {number} [topK] - Number of results to return
   * @returns {Promise<Array>} Search results
   */
  static async searchSimilarMessages(
    projectId,
    threadId,
    query,
    topK = 5
  ) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty results');
        return [];
      }

      const namespace = `${projectId}_${threadId}`;
      const queryEmbedding = await this.generateEmbedding(query);

      const searchResults = await this.index.namespace(namespace).query({
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
      });

      return searchResults.matches || [];
    } catch (error) {
      logger.error('Error searching similar messages in Pinecone:', error);
      return [];
    }
  }

  /**
   * Delete all messages for a thread
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @returns {Promise<void>}
   */
  static async deleteThreadMessages(projectId, threadId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping thread deletion');
        return;
      }

      const namespace = `${projectId}_${threadId}`;

      // Check if namespace exists before attempting deletion
      try {
        const stats = await this.index.describeIndexStats();
        const namespaceExists = stats.namespaces && stats.namespaces[namespace];

        if (!namespaceExists) {
          logger.info(`Namespace ${namespace} does not exist, skipping deletion`);
          return;
        }

        // Delete all vectors in the namespace
        await this.index.namespace(namespace).deleteAll();
        logger.info(`Deleted all messages for thread ${threadId} in project ${projectId}`);
      } catch (deleteError) {
        // Handle 404 errors gracefully - namespace might not exist or be empty
        if (deleteError.name === 'PineconeNotFoundError' || deleteError.message?.includes('404')) {
          logger.info(`Namespace ${namespace} not found or empty, skipping deletion`);
          return;
        }
        throw deleteError;
      }
    } catch (error) {
      logger.error('Error deleting thread messages from Pinecone:', error);
      // Don't throw the error to prevent blocking thread deletion in the database
      // Log the error but continue with the operation
    }
  }

  /**
   * Delete all messages for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<void>}
   */
  static async deleteProjectMessages(projectId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping project deletion');
        return;
      }

      // Note: This is a simplified implementation
      // In a real scenario, you might need to list all namespaces for the project
      // and delete them individually
      
      logger.warn(`Project deletion for ${projectId} - manual cleanup may be required`);
    } catch (error) {
      logger.error('Error deleting project messages from Pinecone:', error);
      throw error;
    }
  }

  /**
   * Get namespace statistics
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @returns {Promise<Object>} Namespace statistics
   */
  static async getNamespaceStats(projectId, threadId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty stats');
        return { vectorCount: 0 };
      }

      const namespace = `${projectId}_${threadId}`;
      const stats = await this.index.describeIndexStats();
      
      return {
        vectorCount: stats.namespaces?.[namespace]?.vectorCount || 0,
        totalVectors: stats.totalVectorCount || 0,
      };
    } catch (error) {
      logger.error('Error getting namespace stats from Pinecone:', error);
      return { vectorCount: 0 };
    }
  }

  /**
   * Update message metadata
   * @param {string} projectId - Project ID
   * @param {string} threadId - Thread ID
   * @param {string} messageId - Message ID
   * @param {Object} metadata - New metadata
   * @returns {Promise<void>}
   */
  static async updateMessageMetadata(projectId, threadId, messageId, metadata) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping metadata update');
        return;
      }

      const namespace = `${projectId}_${threadId}`;
      
      // Fetch existing vector
      const fetchResult = await this.index.namespace(namespace).fetch([messageId]);
      const existingVector = fetchResult.vectors?.[messageId];
      
      if (!existingVector) {
        logger.warn(`Vector ${messageId} not found for metadata update`);
        return;
      }

      // Update with new metadata
      await this.index.namespace(namespace).upsert([
        {
          id: messageId,
          values: existingVector.values,
          metadata: {
            ...existingVector.metadata,
            ...metadata,
            updatedAt: new Date().toISOString(),
          },
        },
      ]);

      logger.debug(`Updated metadata for message: ${messageId}`);
    } catch (error) {
      logger.error('Error updating message metadata in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Batch store multiple messages
   * @param {Array} messages - Array of message objects
   * @returns {Promise<void>}
   */
  static async batchStoreMessages(messages) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping batch storage');
        return;
      }

      const batchSize = 100; // Pinecone batch limit
      
      for (let i = 0; i < messages.length; i += batchSize) {
        const batch = messages.slice(i, i + batchSize);
        
        for (const msg of batch) {
          await this.storeMessage(
            msg.projectId,
            msg.threadId,
            msg.messageId,
            msg.message,
            msg.response,
            msg.metadata
          );
        }
      }

      logger.info(`Batch stored ${messages.length} messages in Pinecone`);
    } catch (error) {
      logger.error('Error batch storing messages in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Store file content in Pinecone with session-based namespace
   * @param {string} content - File content to store
   * @param {Object} metadata - File metadata including sessionId
   * @returns {Promise<void>}
   */
  static async storeFileContent(content, metadata = {}) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, skipping file content storage');
        return;
      }

      // Validate content
      if (!content || typeof content !== 'string' || content.trim().length === 0) {
        logger.warn('Invalid or empty content provided to storeFileContent');
        return;
      }

      const { sessionId, fileName } = metadata;
      if (!sessionId) {
        logger.warn('No sessionId provided for Pinecone storage');
        return;
      }

      // Generate unique file ID
      const fileId = `${Date.now()}_${fileName?.replace(/[^a-zA-Z0-9.-]/g, '_') || 'unknown'}`;

      // Split content into chunks for better semantic search
      const chunks = this.splitTextIntoChunks(content, 1000); // 1000 char chunks
      const namespace = sessionId;

      for (let i = 0; i < chunks.length; i++) {
        const chunkId = `${fileId}_chunk_${i}`;
        const embedding = await this.generateEmbedding(chunks[i]);

        await this.index.namespace(namespace).upsert([
          {
            id: chunkId,
            values: embedding,
            metadata: {
              fileId,
              chunkIndex: i,
              content: chunks[i],
              originalFileName: metadata.fileName || metadata.originalFileName,
              mimeType: metadata.fileType || metadata.mimeType,
              timestamp: new Date().toISOString(),
              userId: metadata.userId,
              s3Key: metadata.s3Key,
              processingMethod: metadata.processingMethod || 'standard',
            },
          },
        ]);
      }

      logger.info(`[Pinecone] Stored ${chunks.length} chunks for file ${fileId} in namespace ${namespace}`);
    } catch (error) {
      logger.error('Error storing file content in Pinecone:', error);
      throw error;
    }
  }

  /**
   * Check if a namespace exists for a given session ID
   * @param {string} sessionId - Session ID for namespace
   * @returns {Promise<boolean>} Whether the namespace exists and has content
   */
  static async namespaceExists(sessionId) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning false for namespace existence check');
        return false;
      }

      const namespace = sessionId;
      const stats = await this.index.describeIndexStats();
      const namespaceExists = stats.namespaces && stats.namespaces[namespace];

      if (namespaceExists && stats.namespaces[namespace].vectorCount > 0) {
        logger.debug(`Namespace ${namespace} exists with ${stats.namespaces[namespace].vectorCount} vectors`);
        return true;
      }

      logger.debug(`Namespace ${namespace} does not exist or is empty`);
      return false;
    } catch (error) {
      logger.error('Error checking namespace existence in Pinecone:', error);
      return false;
    }
  }

  /**
   * Search for relevant content based on user query
   * @param {string} sessionId - Session ID for namespace
   * @param {string} query - User query
   * @param {number} [topK] - Number of results to return
   * @returns {Promise<Array>} Relevant content chunks
   */
  static async searchRelevantContent(sessionId, query, topK = 5) {
    try {
      if (!this.isAvailable()) {
        logger.warn('Pinecone not available, returning empty results');
        return [];
      }

      const namespace = sessionId;
      const queryEmbedding = await this.generateEmbedding(query);

      logger.debug(`[Pinecone] Searching in namespace: ${namespace} with query: "${query.substring(0, 50)}..."`);

      const searchResults = await this.index.namespace(namespace).query({
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
      });

      logger.debug(`[Pinecone] Found ${searchResults.matches?.length || 0} matches in namespace ${namespace}`);

      // Log all match scores for debugging
      if (searchResults.matches && searchResults.matches.length > 0) {
        const scores = searchResults.matches.map(match => match.score);
        logger.debug(`[Pinecone] Match scores: ${scores.join(', ')}`);
      }

      // Use a lower similarity threshold for better recall (0.2 instead of 0.5)
      // In practice, even scores around 0.3-0.4 can be quite relevant
      const similarityThreshold = 0.2;

      const relevantContent = searchResults.matches
        .filter(match => match.score > similarityThreshold)
        .map(match => ({
          content: match.metadata.content,
          score: match.score,
          fileName: match.metadata.originalFileName,
          chunkIndex: match.metadata.chunkIndex,
        }));

      logger.debug(`Found ${relevantContent.length} relevant content chunks for query in namespace ${namespace} (threshold: ${similarityThreshold})`);
      return relevantContent;
    } catch (error) {
      logger.error('Error searching relevant content in Pinecone:', error);
      return [];
    }
  }

  /**
   * Split text into chunks for better embedding storage
   * @param {string} text - Text to split
   * @param {number} maxChunkSize - Maximum chunk size in characters
   * @returns {Array<string>} Text chunks
   */
  static splitTextIntoChunks(text, maxChunkSize = 1000) {
    // Validate input
    if (!text || typeof text !== 'string') {
      logger.warn('Invalid text provided to splitTextIntoChunks');
      return [];
    }

    const chunks = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    let currentChunk = '';

    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      if (currentChunk.length + trimmedSentence.length + 1 <= maxChunkSize) {
        currentChunk += (currentChunk ? '. ' : '') + trimmedSentence;
      } else {
        if (currentChunk) {
          chunks.push(currentChunk + '.');
        }
        currentChunk = trimmedSentence;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk + '.');
    }

    // If no sentences found, split by character limit
    if (chunks.length === 0 && text.length > 0) {
      for (let i = 0; i < text.length; i += maxChunkSize) {
        chunks.push(text.substring(i, i + maxChunkSize));
      }
    }

    return chunks;
  }

  /**
   * Get Pinecone service health status
   * @returns {Promise<Object>} Health status
   */
  static async getHealthStatus() {
    try {
      if (!this.isAvailable()) {
        return {
          status: 'unavailable',
          message: 'Pinecone client not initialized',
        };
      }

      const stats = await this.index.describeIndexStats();

      return {
        status: 'healthy',
        totalVectors: stats.totalVectorCount || 0,
        dimension: stats.dimension || 0,
        namespaces: Object.keys(stats.namespaces || {}),
      };
    } catch (error) {
      logger.error('Error checking Pinecone health:', error);
      return {
        status: 'error',
        message: error.message,
      };
    }
  }
}
