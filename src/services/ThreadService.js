import { ChatThread, ChatMessage, Project } from '../models/chat/index.js';
import { LLMService } from './LLMService.js';
import { PineconeService } from './PineconeService.js';
import { CreditService } from './CreditService.js';
import { ChatService } from './ChatService.js';
import { EncryptionUtil } from '../utils/encryption.js';
import { CREDIT_SYSTEM, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * @typedef {Object} ThreadChatRequest
 * @property {string} message - User message
 * @property {string} [threadId] - Thread ID
 * @property {string} [projectId] - Project ID
 * @property {string} [llmModel] - LLM model to use
 */

/**
 * @typedef {Object} ThreadChatResponse
 * @property {string} response - AI response
 * @property {string} threadId - Thread ID
 * @property {string} sessionId - Session ID
 * @property {string} messageId - Message ID
 * @property {boolean} isProject - Whether this is a project thread
 * @property {string} [projectId] - Project ID if applicable
 */

export class ThreadService {
  /**
   * Process chat message in a thread (regular or project)
   * @param {string} userId - User ID
   * @param {ThreadChatRequest} chatRequest - Chat request data
   * @returns {Promise<ThreadChatResponse>} Thread chat response
   */
  static async processThreadChat(userId, chatRequest) {
    try {
      const { message, threadId, projectId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let thread;
      let project = null;

      if (threadId) {
        // Find existing thread
        thread = await ChatThread.findByIdAndUser(threadId, userId);
        if (!thread) {
          throw new Error('Thread not found');
        }
        
        // Get project if this is a project thread
        if (thread.projectId) {
          project = await Project.findByPk(thread.projectId);
        }
      } else if (projectId) {
        // Create new project thread
        project = await Project.findByIdAndUser(projectId, userId);
        if (!project) {
          throw new Error('Project not found');
        }

        const sessionId = EncryptionUtil.generateSessionId();
        thread = await ChatThread.createThread({
          userId,
          sessionId,
          projectId,
          name: message.substring(0, 100) + (message.length > 50 ? '...' : ''),
        });
        logger.info(`Created new project thread ${thread.id} for project ${projectId}`);
      } else {
        // Create new regular thread
        const sessionId = EncryptionUtil.generateSessionId();
        thread = await ChatThread.createThread({
          userId,
          sessionId,
          name: message.substring(0, 100) + (message.length > 50 ? '...' : ''),
        });
        logger.info(`Created new thread ${thread.id} for user ${userId}`);
      }

      // Get conversation history
      const conversationHistory = await this.getThreadHistory(thread.id);
      
      // Build system prompt with project context if applicable
      let systemPrompt = this.getSystemPrompt();
      if (project) {
        systemPrompt += `\n\nProject Context:\nName: ${project.name}\nDescription: ${project.description}\nRules: ${project.rules}`;
      }

      // Generate response using LLM
      const response = await LLMService.generateResponse(
        message,
        llmModel,
        systemPrompt,
        conversationHistory
      );

      // Save message and response
      const chatMessage = await ChatMessage.createMessage({
        chatId: thread.id,
        message,
        response,
        llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
      });

      // Store in Pinecone for project chats
      if (project) {
        await PineconeService.storeMessage(
          project.id,
          thread.id,
          chatMessage.id,
          message,
          response,
          {
            userId,
            projectName: project.name,
            llmModel: chatMessage.llmModel,
          }
        );
      }

      // Deduct credit
      const creditDeducted = await CreditService.deductCredits(
        userId,
        CREDIT_SYSTEM.CHAT_MESSAGE_COST,
        CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
      );
      
      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing message`);
      }

      logger.info(`Processed thread chat message for user ${userId}, thread: ${thread.id}`);

      return {
        response,
        threadId: thread.id,
        sessionId: thread.sessionId,
        messageId: chatMessage.id,
        isProject: !!project,
        projectId: project?.id,
      };
    } catch (error) {
      logger.error('Error processing thread chat:', error);
      throw error;
    }
  }

  /**
   * Process streaming chat message in a thread
   * @param {string} userId - User ID
   * @param {ThreadChatRequest} chatRequest - Chat request data
   * @param {Object} res - Express response object
   * @returns {Promise<Object>} Thread response metadata
   */
  static async processThreadChatStreaming(userId, chatRequest, res) {
    try {
      const { message, threadId, projectId, llmModel } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let thread;
      let project = null;

      if (threadId) {
        // Find existing thread
        thread = await ChatThread.findByIdAndUser(threadId, userId);
        if (!thread) {
          throw new Error('Thread not found');
        }
        
        // Get project if this is a project thread
        if (thread.projectId) {
          project = await Project.findByPk(thread.projectId);
        }
      } else if (projectId) {
        // Create new project thread
        project = await Project.findByIdAndUser(projectId, userId);
        if (!project) {
          throw new Error('Project not found');
        }

        const sessionId = EncryptionUtil.generateSessionId();
        thread = await ChatThread.createThread({
          userId,
          sessionId,
          projectId,
          name: message.substring(0, 100) + (message.length > 50 ? '...' : ''),
        });
        logger.info(`Created new project thread ${thread.id} for project ${projectId}`);
      } else {
        // Create new regular thread
        const sessionId = EncryptionUtil.generateSessionId();
        thread = await ChatThread.createThread({
          userId,
          sessionId,
          name: message.substring(0, 100) + (message.length > 50 ? '...' : ''),
        });
        logger.info(`Created new thread ${thread.id} for user ${userId}`);
      }

      // Get conversation history
      const conversationHistory = await this.getThreadHistory(thread.id);
      
      // Build system prompt with project context and automatic RAG integration
      let systemPrompt = await ChatService.getSystemPrompt(thread.sessionId, message);
      if (project) {
        systemPrompt += `\n\nProject Context:\nName: ${project.name}\nDescription: ${project.description}\nRules: ${project.rules}`;
      }

      // Prepare metadata
      const metadata = {
        threadId: thread.id,
        sessionId: thread.sessionId,
        userId,
        isProject: !!project,
        projectId: project?.id,
      };

      // Generate streaming response
      const response = await LLMService.generateStreamingResponse(
        message,
        res,
        llmModel,
        systemPrompt,
        conversationHistory,
        metadata,
        async (fullResponse) => {
          // Save message and response
          const chatMessage = await ChatMessage.createMessage({
            chatId: thread.id,
            message,
            response: fullResponse,
            llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
          });

          // Store in Pinecone for project chats
          if (project) {
            await PineconeService.storeMessage(
              project.id,
              thread.id,
              chatMessage.id,
              message,
              fullResponse,
              {
                userId,
                projectName: project.name,
                llmModel: chatMessage.llmModel,
              }
            );
          }

          // Return updated metadata with messageId
          return {
            messageId: chatMessage.id
          };
        }
      );

      // Deduct credit after successful completion
      const creditDeducted = await CreditService.deductCredits(
        userId,
        CREDIT_SYSTEM.CHAT_MESSAGE_COST,
        CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
      );
      
      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing streaming message`);
      }

      logger.info(`Processed streaming thread chat message for user ${userId}, thread: ${thread.id}`);

      return {
        threadId: thread.id,
        sessionId: thread.sessionId,
        messageId: 'will-be-set-in-callback',
        isProject: !!project,
        projectId: project?.id,
      };
    } catch (error) {
      logger.error('Error processing streaming thread chat:', error);
      throw error;
    }
  }
  /**
   * Process streaming chat message in a thread with file attachment support
   * @param {string} userId - User ID
   * @param {ThreadChatRequest} chatRequest - Chat request data
   * @param {Object} res - Express response object
   * @param {Object} [attachedFile] - Attached file data
   * @returns {Promise<Object>} Thread response metadata
   */
  static async processThreadChatStreamingWithAttachment(userId, chatRequest, res, attachedFile) {
    try {
      const { message, threadId, projectId, llmModel, ns } = chatRequest;

      if (!message || message.trim().length === 0) {
        throw new Error('Message cannot be empty');
      }

      // Check if user has enough credits
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      let thread;
      let project = null;

      if (threadId) {
        // Find existing thread
        thread = await ChatThread.findByIdAndUser(threadId, userId);
        if (!thread) {
          throw new Error('Thread not found');
        }

        // Get project if this is a project thread
        if (thread.projectId) {
          project = await Project.findByPk(thread.projectId);
        }
      } else if (projectId) {
        // Create new project thread
        project = await Project.findByIdAndUser(projectId, userId);
        if (!project) {
          throw new Error('Project not found');
        }

        const sessionId = EncryptionUtil.generateSessionId();
        thread = await ChatThread.createThread({
          userId,
          sessionId,
          projectId,
          name: message.substring(0, 100) + (message.length > 50 ? '...' : ''),
        });
        logger.info(`Created new project thread ${thread.id} for project ${projectId}`);
      } else {
        // Create new regular thread
        const sessionId = EncryptionUtil.generateSessionId();
        thread = await ChatThread.createThread({
          userId,
          sessionId,
          name: message.substring(0, 100) + (message.length > 50 ? '...' : ''),
        });
        logger.info(`Created new thread ${thread.id} for user ${userId}`);
      }

      // Get conversation history
      const conversationHistory = await this.getThreadHistory(thread.id);

      // Build system prompt with project context and automatic RAG integration
      let systemPrompt = await ChatService.getSystemPrompt(thread.sessionId, message);
      if (project) {
        systemPrompt += `\n\nProject Context:\nName: ${project.name}\nDescription: ${project.description}\nRules: ${project.rules}`;
      }

      // Prepare metadata
      const metadata = {
        threadId: thread.id,
        sessionId: thread.sessionId,
        userId,
        isProject: !!project,
        projectId: project?.id,
      };

      // Generate streaming response with file attachment
      const response = await LLMService.generateStreamingResponse(
          message,
          res,
          llmModel,
          systemPrompt,
          conversationHistory,
          metadata,
          async (fullResponse) => {
            // Save message and response with attachment metadata
            const messageData = {
              chatId: thread.id,
              message,
              response: fullResponse,
              llmModel: llmModel || process.env.DEFAULT_LLM_MODEL || 'gpt-3.5-turbo',
            };

            // Add attachment metadata if file was attached
            if (attachedFile && attachedFile.metadata) {
              messageData.attachmentPath = attachedFile.filePath;
              messageData.attachmentName = attachedFile.metadata.originalName;
              messageData.attachmentType = attachedFile.metadata.mimeType;
              messageData.attachmentSize = attachedFile.metadata.size;
              messageData.attachmentS3Url = attachedFile.metadata.s3Url;
              messageData.attachmentS3Key = attachedFile.metadata.s3Key;
              messageData.attachmentStorageType = attachedFile.metadata.storageType;
              messageData.attachmentSecureId = attachedFile.metadata.secureFileId;
            }

            const chatMessage = await ChatMessage.createMessage(messageData);

            // Store in Pinecone for project chats
            if (project) {
              await PineconeService.storeMessage(
                  project.id,
                  thread.id,
                  chatMessage.id,
                  message,
                  fullResponse,
                  {
                    userId,
                    projectName: project.name,
                    llmModel: chatMessage.llmModel,
                  }
              );
            }

            // Return updated metadata with messageId
            return {
              messageId: chatMessage.id
            };
          },
          attachedFile
      );

      // Deduct credit after successful completion
      const creditDeducted = await CreditService.deductCredits(
          userId,
          CREDIT_SYSTEM.CHAT_MESSAGE_COST,
          CREDIT_SYSTEM.DESCRIPTIONS.CHAT_MESSAGE
      );

      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after processing streaming message with attachment`);
      }

      logger.info(`Processed streaming thread chat message with attachment for user ${userId}, thread: ${thread.id}`);

      return {
        threadId: thread.id,
        sessionId: thread.sessionId,
        messageId: 'will-be-set-in-callback',
        isProject: !!project,
        projectId: project?.id,
      };
    } catch (error) {
      logger.error('Error processing streaming thread chat with attachment:', error);
      throw error;
    }
  }

  /**
   * Get thread history
   * @param {string} threadId - Thread ID
   * @param {number} [limit] - Limit number of messages
   * @returns {Promise<Array>} Thread history
   */
  static async getThreadHistory(threadId, limit = 10) {
    try {
      const messages = await ChatMessage.findAll({
        where: { chatId: threadId },
        order: [['createdAt', 'ASC']],
        limit,
      });

      return messages.map(msg => [
        { role: 'user', content: msg.message },
        { role: 'assistant', content: msg.response }
      ]).flat();
    } catch (error) {
      logger.error('Error getting thread history:', error);
      return [];
    }
  }

  /**
   * Get system prompt for threads
   * @returns {string} System prompt
   */
  static getSystemPrompt() {
    return 'You are a helpful assistant. Provide accurate, helpful, and concise responses to user questions. Use proper delimiters for Latex Expressions($...$ for inline math and $$...$$ for block math).';
  }

  /**
   * Get user threads
   * @param {string} userId - User ID
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} User threads
   */
  static async getUserThreads(userId, limit = 20, offset = 0) {
    try {
      return await ChatThread.findAll({
        where: { userId },
        order: [['updatedAt', 'DESC']],
        limit,
        offset,
      });
    } catch (error) {
      logger.error('Error getting user threads:', error);
      throw error;
    }
  }

  /**
   * Get project threads
   * @param {string} projectId - Project ID
   * @param {string} userId - User ID
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} Project threads
   */
  static async getProjectThreads(projectId, userId, limit = 20, offset = 0) {
    try {
      // Verify user owns the project
      const project = await Project.findByIdAndUser(projectId, userId);
      if (!project) {
        throw new Error('Project not found');
      }

      return await ChatThread.findProjectThreads(projectId, limit, offset);
    } catch (error) {
      logger.error('Error getting project threads:', error);
      throw error;
    }
  }

  /**
   * Get thread with messages
   * @param {string} threadId - Thread ID
   * @param {string} userId - User ID
   * @param {number} [messageLimit] - Limit messages
   * @param {number} [messageOffset] - Offset for message pagination
   * @returns {Promise<Object>} Thread with messages
   */
  static async getThreadWithMessages(threadId, userId, messageLimit = 50, messageOffset = 0) {
    try {
      const thread = await ChatThread.findByIdAndUser(threadId, userId);
      if (!thread) {
        throw new Error('Thread not found');
      }

      const messages = await ChatMessage.findAll({
        where: { chatId: threadId },
        order: [['createdAt', 'ASC']],
        limit: messageLimit,
        offset: messageOffset,
      });

      // Transform messages for secure output
      const secureMessages = messages.map(message => ChatMessage.transformForSecureOutput(message));

      return {
        ...thread.toJSON(),
        messages: secureMessages,
      };
    } catch (error) {
      logger.error('Error getting thread with messages:', error);
      throw error;
    }
  }

  /**
   * Update thread title
   * @param {string} threadId - Thread ID
   * @param {string} userId - User ID
   * @param {string} title - New title
   * @returns {Promise<Object>} Updated thread
   */
  static async updateThreadTitle(threadId, userId, title) {
    try {
      const thread = await ChatThread.findByIdAndUser(threadId, userId);
      if (!thread) {
        throw new Error('Thread not found');
      }

      if (!title || title.trim().length === 0) {
        throw new Error('Title cannot be empty');
      }

      await thread.update({ title: title.trim() });

      logger.info(`Updated thread title for thread ${threadId}`);
      return thread;
    } catch (error) {
      logger.error('Error updating thread title:', error);
      throw error;
    }
  }

  /**
   * Delete thread and all messages
   * @param {string} threadId - Thread ID
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async deleteThread(threadId, userId) {
    try {
      const thread = await ChatThread.findByIdAndUser(threadId, userId);
      if (!thread) {
        throw new Error('Thread not found');
      }

      // Delete Pinecone data if this is a project thread
      if (thread.projectId) {
        await PineconeService.deleteThreadMessages(thread.projectId, threadId);
      }

      // Delete all messages
      await ChatMessage.destroy({
        where: { chatId: threadId }
      });

      // Delete thread
      await thread.destroy();

      logger.info(`Deleted thread ${threadId} for user ${userId}`);
    } catch (error) {
      logger.error('Error deleting thread:', error);
      throw error;
    }
  }

  /**
   * Search threads by title
   * @param {string} userId - User ID
   * @param {string} searchTerm - Search term
   * @param {number} [limit] - Limit results
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} Matching threads
   */
  static async searchThreads(userId, searchTerm, limit = 20, offset = 0) {
    try {
      const { Op } = await import('sequelize');

      return await ChatThread.findAll({
        where: {
          userId,
          title: {
            [Op.like]: `%${searchTerm}%`
          }
        },
        order: [['updatedAt', 'DESC']],
        limit,
        offset,
      });
    } catch (error) {
      logger.error('Error searching threads:', error);
      throw error;
    }
  }

  /**
   * Get thread statistics
   * @param {string} threadId - Thread ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Thread statistics
   */
  static async getThreadStats(threadId, userId) {
    try {
      const thread = await ChatThread.findByIdAndUser(threadId, userId);
      if (!thread) {
        throw new Error('Thread not found');
      }

      const messageCount = await ChatMessage.count({
        where: { chatId: threadId }
      });

      const lastMessage = await ChatMessage.findOne({
        where: { chatId: threadId },
        order: [['createdAt', 'DESC']],
        attributes: ['createdAt']
      });

      return {
        messageCount,
        lastActivity: lastMessage?.createdAt || thread.createdAt,
        isProject: !!thread.projectId,
        projectId: thread.projectId,
      };
    } catch (error) {
      logger.error('Error getting thread stats:', error);
      throw error;
    }
  }

  /**
   * Regenerate thread message response
   * @param {string} userId - User ID
   * @param {string} messageId - Message ID to regenerate
   * @returns {Promise<Object>} Regenerated response
   */
  static async regenerateThreadMessage(userId, messageId) {
    try {
      if (!messageId) {
        throw new Error('Message ID is required');
      }

      // Find the message and verify ownership
      const chatMessage = await ChatMessage.findByPk(messageId, {
        include: [{
          model: ChatThread,
          where: { userId },
        }]
      });

      if (!chatMessage) {
        throw new Error('Message not found');
      }

      // Check if user has enough credits
      const hasCredits = await CreditService.hasEnoughCredits(userId, CREDIT_SYSTEM.CHAT_MESSAGE_COST);
      if (!hasCredits) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_CREDITS);
      }

      const thread = chatMessage.ChatThread;
      let project = null;

      // Get project if this is a project thread
      if (thread.projectId) {
        project = await Project.findByPk(thread.projectId);
      }

      // Get conversation history up to this message
      const conversationHistory = await this.getThreadHistoryBeforeMessage(thread.id, messageId);

      // Build system prompt with project context if applicable
      let systemPrompt = this.getSystemPrompt();
      if (project) {
        systemPrompt += `\n\nProject Context:\nName: ${project.name}\nDescription: ${project.description}\nRules: ${project.rules}`;
      }

      // Generate new response
      const newResponse = await LLMService.generateResponse(
          chatMessage.message,
          chatMessage.llmModel,
          systemPrompt,
          conversationHistory
      );

      // Update the message with new response
      await chatMessage.update({ response: newResponse });

      // Update Pinecone if this is a project thread
      if (project) {
        await PineconeService.storeMessage(
            project.id,
            thread.id,
            chatMessage.id,
            chatMessage.message,
            newResponse,
            {
              userId,
              projectName: project.name,
              llmModel: chatMessage.llmModel,
              regenerated: true,
            }
        );
      }

      // Deduct credit
      const creditDeducted = await CreditService.deductCredits(
          userId,
          CREDIT_SYSTEM.CHAT_MESSAGE_COST,
          'Thread message regeneration'
      );

      if (!creditDeducted) {
        logger.error(`Failed to deduct credit for user ${userId} after regenerating thread message`);
      }

      logger.info(`Regenerated thread message ${messageId} for user ${userId}`);

      return {
        messageId: chatMessage.id,
        response: newResponse,
      };
    } catch (error) {
      logger.error('Error regenerating thread message:', error);
      throw error;
    }
  }

  /**
   * Get thread history before a specific message
   * @param {string} threadId - Thread ID
   * @param {string} messageId - Message ID
   * @returns {Promise<Array>} Thread history before message
   */
  static async getThreadHistoryBeforeMessage(threadId, messageId) {
    try {
      const { Op } = await import('sequelize');

      const messages = await ChatMessage.findAll({
        where: {
          chatId: threadId,
          id: {
            [Op.ne]: messageId
          }
        },
        order: [['createdAt', 'ASC']],
      });

      return messages.map(msg => [
        { role: 'user', content: msg.message },
        { role: 'assistant', content: msg.response }
      ]).flat();
    } catch (error) {
      logger.error('Error getting thread history before message:', error);
      return [];
    }
  }

  /**
   * Get thread messages with pagination
   * @param {string} threadId - Thread ID
   * @param {string} userId - User ID
   * @param {number} [limit] - Limit number of messages
   * @param {number} [offset] - Offset for pagination
   * @returns {Promise<Array>} Thread messages
   */
  static async getThreadMessages(threadId, userId, limit = 50, offset = 0) {
    try {
      // Verify thread ownership
      const thread = await ChatThread.findByIdAndUser(threadId, userId);
      if (!thread) {
        throw new Error('Thread not found');
      }

      const messages = await ChatMessage.findAll({
        where: { chatId: threadId },
        order: [['createdAt', 'ASC']],
        limit,
        offset,
      });

      // Transform messages for secure output
      return messages.map(message => ChatMessage.transformForSecureOutput(message));
    } catch (error) {
      logger.error('Error getting thread messages:', error);
      throw error;
    }
  }

  /**
   * Update thread name
   * @param {string} threadId - Thread ID
   * @param {string} userId - User ID
   * @param {string} name - New thread name
   * @returns {Promise<Object>} Updated thread
   */
  static async updateThreadName(threadId, userId, name) {
    try {
      // Verify thread ownership
      const thread = await ChatThread.findByIdAndUser(threadId, userId);
      if (!thread) {
        throw new Error('Thread not found');
      }

      // Update thread name
      await thread.update({ name: name.trim() });

      logger.info(`Thread name updated: ${threadId} by user ${userId}`);
      return thread;
    } catch (error) {
      logger.error('Error updating thread name:', error);
      throw error;
    }
  }
}
